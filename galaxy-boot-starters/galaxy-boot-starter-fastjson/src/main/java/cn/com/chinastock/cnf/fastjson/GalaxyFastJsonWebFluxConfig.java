package cn.com.chinastock.cnf.fastjson;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Decoder;
import com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Encoder;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.lang.NonNull;
import org.springframework.web.reactive.config.WebFluxConfigurer;

import java.util.ArrayList;

/**
 * Galaxy FastJSON WebFlux 自动配置类
 * <p>
 * 为 Spring WebFlux 应用提供 FastJSON 支持，替换默认的 Jackson。
 * 该配置类只在响应式 Web 应用环境中生效，避免与 MVC 配置冲突。
 * </p>
 * <p>
 * 主要功能：
 * <ul>
 *   <li>创建 WebFlux 专用的 FastJSON 配置</li>
 *   <li>提供 Fastjson2Encoder 和 Fastjson2Decoder Bean</li>
 *   <li>通过 WebFluxConfigurer 注册自定义编解码器</li>
 *   <li>支持所有 FastJsonProperties 配置项</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Boot Team
 * @since 0.2.6-ALPHA2
 * @see GalaxyFastJsonConfig MVC 版本的配置类
 * @see FastJsonProperties FastJSON 配置属性
 */
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
@ConditionalOnClass({
    org.springframework.web.reactive.config.WebFluxConfigurer.class,
    com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Decoder.class,
    com.alibaba.fastjson2.support.spring6.http.codec.Fastjson2Encoder.class
})
@EnableConfigurationProperties(FastJsonProperties.class)
public class GalaxyFastJsonWebFluxConfig implements WebFluxConfigurer {

    @Autowired
    private FastJsonProperties fastJsonProperties;

    /**
     * 初始化 FastJSON 全局配置
     * <p>
     * 配置全局的日期时间格式，解决 LocalDateTime 序列化问题。
     * 这个配置会影响所有使用 FastJSON 的序列化操作。
     * </p>
     */
    @PostConstruct
    public void initFastJsonGlobalConfig() {
        JSON.config(JSONWriter.Feature.WriteBigDecimalAsPlain);
        JSON.config(JSONWriter.Feature.IgnoreNonFieldGetter);
    }

    /**
     * 收集序列化特性配置
     * <p>
     * 复用 MVC 配置的逻辑，确保配置的一致性。
     * 需要同步修改：{@link cn.com.chinastock.cnf.security.output.FastJsonFilterUtil#handleFastJsonResponse}
     * </p>
     *
     * @return 序列化特性集合
     * @see <a href="https://github.com/alibaba/fastjson2/wiki/fastjson_1_upgrade_cn">FASTJSON 1.x升级指南</a>
     * @see <a href="https://github.com/alibaba/fastjson2/wiki/Features_cn">通过Features配置序列化和反序列化的行为</a>
     */
    private ArrayList<JSONWriter.Feature> collectWriterFromProperties() {
        ArrayList<JSONWriter.Feature> features = new java.util.ArrayList<>();
        // 需要同步修改 cn.com.chinastock.cnf.security.output.FastJsonFilterUtil
        features.add(JSONWriter.Feature.WriteBigDecimalAsPlain);
        features.add(JSONWriter.Feature.IgnoreNonFieldGetter);

        if (fastJsonProperties.isWriteMapNullValue()) {
            features.add(JSONWriter.Feature.WriteMapNullValue);
        }

        if (fastJsonProperties.isWriteNullStringAsEmpty()) {
            features.add(JSONWriter.Feature.WriteNullStringAsEmpty);
        }

        return features;
    }

    /**
     * 收集反序列化特性配置
     * <p>
     * 复用 MVC 配置的逻辑，确保配置的一致性。
     * 需要同步修改：{@link cn.com.chinastock.cnf.security.output.FastJsonFilterUtil}
     * </p>
     *
     * @return 反序列化特性集合
     */
    private java.util.ArrayList<JSONReader.Feature> collectReaderFromProperties() {
        java.util.ArrayList<JSONReader.Feature> features = new java.util.ArrayList<>();

        if (fastJsonProperties.isSupportSmartMatch()) {
            features.add(JSONReader.Feature.SupportSmartMatch);
        }

        return features;
    }

    /**
     * 创建 WebFlux 专用的 FastJSON 配置
     * <p>
     * 复用 MVC 配置的逻辑，确保配置的一致性。
     * 该配置会被 Fastjson2Encoder 和 Fastjson2Decoder 使用。
     * </p>
     *
     * @return WebFlux 专用的 FastJSON 配置
     */
    @Bean
    public FastJsonConfig webfluxFastJsonConfig() {
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setCharset(java.nio.charset.StandardCharsets.UTF_8);
        fastJsonConfig.setWriterFeatures(collectWriterFromProperties().toArray(new JSONWriter.Feature[0]));
        fastJsonConfig.setReaderFeatures(collectReaderFromProperties().toArray(new JSONReader.Feature[0]));

        return fastJsonConfig;
    }

    // 移除 CodecCustomizer，改用 configureHttpMessageCodecs 方法统一配置

    /**
     * 创建 FastJSON WebFlux 编码器
     * <p>
     * 用于将 Java 对象序列化为 JSON 响应体。
     * 支持 application/json 和 application/*+json 媒体类型。
     * </p>
     * <p>
     * 关键改进：FastJSON2 通过全局配置生效，编码器只需要 ObjectMapper 参数。
     * </p>
     *
     * @return FastJSON WebFlux 编码器
     */
    @Bean
    public Fastjson2Encoder fastjson2Encoder() {
        ObjectMapper objectMapper = webfluxObjectMapper();
        return new Fastjson2Encoder(objectMapper);
    }

    /**
     * 创建 FastJSON WebFlux 解码器
     * <p>
     * 用于将 JSON 请求体反序列化为 Java 对象。
     * 支持 application/json 和 application/*+json 媒体类型。
     * </p>
     * <p>
     * 关键改进：FastJSON2 通过全局配置生效，解码器只需要 ObjectMapper 参数。
     * 内存限制通过 configureHttpMessageCodecs 方法统一设置。
     * </p>
     *
     * @return FastJSON WebFlux 解码器
     */
    @Bean
    public Fastjson2Decoder fastjson2Decoder() {
        ObjectMapper objectMapper = webfluxObjectMapper();
        return new Fastjson2Decoder(objectMapper);
    }

    /**
     * 配置 HTTP 消息编解码器
     * <p>
     * 参考您提供的 Kotlin 代码，改进编解码器配置方式。
     * 关键改进：
     * <ul>
     *   <li>注册自定义编解码器到 customCodecs</li>
     *   <li>正确替换默认的 Jackson 编解码器</li>
     * </ul>
     * </p>
     *
     * @param configurer 服务器编解码器配置器
     */
    @Override
    public void configureHttpMessageCodecs(@NonNull ServerCodecConfigurer configurer) {
        configurer.defaultCodecs().jackson2JsonEncoder(fastjson2Encoder());
        configurer.defaultCodecs().jackson2JsonDecoder(fastjson2Decoder());
    }

    /**
     * 创建配置了 JSR310 模块的 ObjectMapper Bean
     * <p>
     * 这个 Bean 用于支持 Jackson 编码器处理 LocalDateTime 等 Java 8 时间类型。
     * 虽然我们主要使用 FastJSON，但 Spring WebFlux 可能仍会使用 Jackson 编码器，
     * 因此需要确保 Jackson 也能正确处理时间类型。
     * </p>
     * <p>
     * 注意：移除 @Primary 注解，避免与 Spring Boot 自动配置的 ObjectMapper 冲突。
     * </p>
     *
     * @return 配置了 JSR310 模块的 ObjectMapper
     */
    @Bean("webfluxObjectMapper")
    public ObjectMapper webfluxObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        return mapper;
    }
}
